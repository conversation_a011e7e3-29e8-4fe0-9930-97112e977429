/*
Copyright 2025 ETCD Operator Team.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package etcd

import (
	"fmt"
	"sort"
	"strings"
)

// Member 表示etcd集群中的一个成员
type Member struct {
	// Name 成员名称，通常与Pod名称相同
	Name string
	// Namespace Kubernetes命名空间
	Namespace string
	// ID etcd成员ID
	ID uint64
	// SecurePeer 是否启用peer TLS
	SecurePeer bool
	// SecureClient 是否启用client TLS
	SecureClient bool
	// ClusterDomain 集群域名
	ClusterDomain string
}

// PeerURL 返回成员的peer URL
func (m *Member) PeerURL() string {
	scheme := "http"
	if m.SecurePeer {
		scheme = "https"
	}
	return fmt.Sprintf("%s://%s.%s.%s.svc.%s:2380", scheme, m.Name, m.Name, m.Namespace, m.ClusterDomain)
}

// ClientURL 返回成员的client URL
func (m *Member) ClientURL() string {
	scheme := "http"
	if m.SecureClient {
		scheme = "https"
	}
	return fmt.Sprintf("%s://%s.%s.%s.svc.%s:2379", scheme, m.Name, m.Name, m.Namespace, m.ClusterDomain)
}

// MemberSet 表示etcd成员集合
type MemberSet map[string]*Member

// NewMemberSet 创建新的成员集合
func NewMemberSet() MemberSet {
	return make(MemberSet)
}

// Add 向集合中添加成员
func (ms MemberSet) Add(m *Member) {
	ms[m.Name] = m
}

// Remove 从集合中移除成员
func (ms MemberSet) Remove(name string) {
	delete(ms, name)
}

// Size 返回集合大小
func (ms MemberSet) Size() int {
	return len(ms)
}

// IsEqual 检查两个成员集合是否相等
func (ms MemberSet) IsEqual(other MemberSet) bool {
	if ms.Size() != other.Size() {
		return false
	}

	for name := range ms {
		if _, exists := other[name]; !exists {
			return false
		}
	}

	return true
}

// Diff 返回当前集合与另一个集合的差集
func (ms MemberSet) Diff(other MemberSet) MemberSet {
	diff := NewMemberSet()
	for name, member := range ms {
		if _, exists := other[name]; !exists {
			diff.Add(member)
		}
	}
	return diff
}

// PickOne 随机选择一个成员（用于缩容时选择要移除的成员）
func (ms MemberSet) PickOne() *Member {
	for _, member := range ms {
		return member
	}
	return nil
}

// Names 返回所有成员名称的切片
func (ms MemberSet) Names() []string {
	names := make([]string, 0, len(ms))
	for name := range ms {
		names = append(names, name)
	}
	sort.Strings(names)
	return names
}

// PeerURLPairs 返回所有成员的peer URL对，格式为"name=url"
func (ms MemberSet) PeerURLPairs() []string {
	pairs := make([]string, 0, len(ms))
	for _, member := range ms {
		pairs = append(pairs, fmt.Sprintf("%s=%s", member.Name, member.PeerURL()))
	}
	sort.Strings(pairs)
	return pairs
}

// ClientURLs 返回所有成员的client URL列表
func (ms MemberSet) ClientURLs() []string {
	urls := make([]string, 0, len(ms))
	for _, member := range ms {
		urls = append(urls, member.ClientURL())
	}
	sort.Strings(urls)
	return urls
}

// String 返回成员集合的字符串表示
func (ms MemberSet) String() string {
	names := ms.Names()
	return fmt.Sprintf("[%s]", strings.Join(names, ", "))
}

// NewMember 创建新的etcd成员
func NewMember(name, namespace string, securePeer, secureClient bool, clusterDomain string) *Member {
	if clusterDomain == "" {
		clusterDomain = "cluster.local"
	}

	return &Member{
		Name:          name,
		Namespace:     namespace,
		SecurePeer:    securePeer,
		SecureClient:  secureClient,
		ClusterDomain: clusterDomain,
	}
}

// MemberFromPodName 从Pod名称创建成员（用于从运行的Pod构建成员集合）
func MemberFromPodName(podName, namespace string, securePeer, secureClient bool) *Member {
	return NewMember(podName, namespace, securePeer, secureClient, "cluster.local")
}

// GenerateMemberName 生成新的成员名称
func GenerateMemberName(clusterName string) string {
	// 使用时间戳生成唯一的成员名称
	// 在实际实现中，可能需要使用更复杂的命名策略
	return fmt.Sprintf("%s-%d", clusterName, generateRandomSuffix())
}

// generateRandomSuffix 生成随机后缀（简化实现）
func generateRandomSuffix() int64 {
	// 这里使用简化的实现，实际应该使用更好的随机数生成
	// 或者使用Kubernetes的命名约定
	return 1000 + int64(len("random"))
}

// ValidateMemberSet 验证成员集合的有效性
func ValidateMemberSet(ms MemberSet, expectedSize int) error {
	if ms.Size() == 0 {
		return fmt.Errorf("member set is empty")
	}

	if expectedSize > 0 && ms.Size() != expectedSize {
		return fmt.Errorf("member set size %d does not match expected size %d", ms.Size(), expectedSize)
	}

	// 检查成员名称的唯一性
	names := make(map[string]bool)
	for _, member := range ms {
		if names[member.Name] {
			return fmt.Errorf("duplicate member name: %s", member.Name)
		}
		names[member.Name] = true
	}

	return nil
}

// FilterReadyMembers 过滤出就绪的成员（这里简化实现，实际需要检查Pod状态）
func FilterReadyMembers(ms MemberSet) MemberSet {
	// 在实际实现中，这里应该检查对应Pod的就绪状态
	// 现在简化为返回所有成员
	ready := NewMemberSet()
	for _, member := range ms {
		ready.Add(member)
	}
	return ready
}
