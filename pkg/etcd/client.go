// Copyright 2025 ETCD Operator Team.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package etcd

import (
	"context"
	"crypto/tls"
	"fmt"
	"time"

	clientv3 "go.etcd.io/etcd/client/v3"
)

const (
	// DefaultDialTimeout etcd客户端连接超时时间
	DefaultDialTimeout = 5 * time.Second
	// DefaultRequestTimeout etcd请求超时时间
	DefaultRequestTimeout = 5 * time.Second
)

// Client etcd客户端封装
type Client struct {
	*clientv3.Client
	endpoints []string
	tlsConfig *tls.Config
}

// NewClient 创建新的etcd客户端
func NewClient(endpoints []string, tlsConfig *tls.Config) (*Client, error) {
	cfg := clientv3.Config{
		Endpoints:   endpoints,
		DialTimeout: DefaultDialTimeout,
		TLS:         tlsConfig,
	}

	cli, err := clientv3.New(cfg)
	if err != nil {
		return nil, fmt.Errorf("创建etcd客户端失败: %v", err)
	}

	return &Client{
		Client:    cli,
		endpoints: endpoints,
		tlsConfig: tlsConfig,
	}, nil
}

// ListMembers 列出etcd集群所有成员
func (c *Client) ListMembers(ctx context.Context) (*clientv3.MemberListResponse, error) {
	ctx, cancel := context.WithTimeout(ctx, DefaultRequestTimeout)
	defer cancel()

	resp, err := c.Client.MemberList(ctx)
	if err != nil {
		return nil, fmt.Errorf("获取etcd成员列表失败: %v", err)
	}

	return resp, nil
}

// AddMember 向etcd集群添加新成员
func (c *Client) AddMember(ctx context.Context, peerURLs []string) (*clientv3.MemberAddResponse, error) {
	ctx, cancel := context.WithTimeout(ctx, DefaultRequestTimeout)
	defer cancel()

	resp, err := c.Client.MemberAdd(ctx, peerURLs)
	if err != nil {
		return nil, fmt.Errorf("添加etcd成员失败: %v", err)
	}

	return resp, nil
}

// RemoveMember 从etcd集群移除成员
func (c *Client) RemoveMember(ctx context.Context, memberID uint64) (*clientv3.MemberRemoveResponse, error) {
	ctx, cancel := context.WithTimeout(ctx, DefaultRequestTimeout)
	defer cancel()

	resp, err := c.Client.MemberRemove(ctx, memberID)
	if err != nil {
		return nil, fmt.Errorf("移除etcd成员失败: %v", err)
	}

	return resp, nil
}

// IsHealthy 检查etcd集群健康状态
func (c *Client) IsHealthy(ctx context.Context) error {
	ctx, cancel := context.WithTimeout(ctx, DefaultRequestTimeout)
	defer cancel()

	// 尝试获取集群状态
	_, err := c.Client.Status(ctx, c.endpoints[0])
	if err != nil {
		return fmt.Errorf("etcd集群不健康: %v", err)
	}

	return nil
}

// Close 关闭etcd客户端连接
func (c *Client) Close() error {
	if c.Client != nil {
		return c.Client.Close()
	}
	return nil
}

// GetEndpoints 获取etcd端点列表
func (c *Client) GetEndpoints() []string {
	return c.endpoints
}

// GetTLSConfig 获取TLS配置
func (c *Client) GetTLSConfig() *tls.Config {
	return c.tlsConfig
}
